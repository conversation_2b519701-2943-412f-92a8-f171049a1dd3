/**
 * 模态登录表单样式 - 320x480像素规格
 */

/* 模态遮罩层 */
.modal-login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", sans-serif;
}

/* 模态容器 - 精确320x480像素 */
.modal-login-container {
  position: relative;
  width: 320px;
  height: 480px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 15%);
  padding: 40px 32px 32px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 关闭按钮 */
.modal-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 错误提示 */
.modal-error-alert {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
  font-size: 14px;
}

.modal-error-alert .error-text {
  color: #dc2626;
}

/* 登录表单 */
.modal-login-form {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 输入框组 */
.modal-input-group {
  margin-bottom: 16px;
}

/* 输入框 - 精确36px高度 */
.modal-input {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #111827;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.modal-input:focus {
  outline: none;
  border-color: #0084ff;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 10%);
}

.modal-input::placeholder {
  color: #9ca3af;
}

/* 密码输入框包装器 */
.modal-password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.modal-password-input {
  padding-right: 40px;
}

.modal-password-toggle {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  transition: color 0.2s ease;
  border-radius: 4px;
}

.modal-password-toggle:hover {
  color: #6b7280;
  background: #f9fafb;
}

.modal-password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 登录按钮 - 100px间距 */
.modal-login-btn {
  width: 100%;
  height: 40px;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 100px; /* 距离密码框100px */
  margin-bottom: 44px; /* 距离复选框44px */
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-login-btn:hover:not(:disabled) {
  background-color: #0066cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 132, 255, 30%);
}

.modal-login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.modal-login-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 记住我复选框组 */
.modal-remember-group {
  display: flex;
  justify-content: center;
  margin-top: auto; /* 推到底部 */
}

.modal-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
  color: #374151;
}

.modal-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.modal-checkbox-custom {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 3px;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.modal-checkbox-label:hover .modal-checkbox-custom {
  border-color: #0084ff;
}

.modal-checkbox-input:checked + .modal-checkbox-custom {
  background-color: #0084ff;
  border-color: #0084ff;
}

.modal-checkbox-input:checked + .modal-checkbox-custom::after {
  content: "";
  position: absolute;
  display: block;
  left: 4px;
  top: 0;
  width: 3px;
  height: 7px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.modal-checkbox-text {
  font-size: 14px;
  color: #374151;
}

/* 响应式调整 */
@media (max-width: 360px) {
  .modal-login-container {
    width: 300px;
    height: 460px;
    padding: 32px 24px 24px;
  }

  .modal-login-btn {
    margin-top: 80px;
    margin-bottom: 36px;
  }
}

/* 动画效果 */
.modal-login-overlay {
  animation: modalFadeIn 0.3s ease-out;
}

.modal-login-container {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
