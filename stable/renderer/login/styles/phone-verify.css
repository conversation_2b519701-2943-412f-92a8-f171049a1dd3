/**
 * 手机号验证码界面样式
 * 根据设计稿实现的卡片式验证界面
 */

/* 验证码卡片容器 - 适配现有的design-login-container */
.phone-verify-card {
  position: relative;
  width: 100%;
  height: 100%;
  background: transparent;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  -webkit-app-region: no-drag;
}

/* 顶部导航区域 */
.phone-verify-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  margin-bottom: 20px;
  background: transparent;
  flex-shrink: 0;
}

/* 返回按钮 */
.nav-back-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #333333;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.nav-back-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.nav-back-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-arrow {
  font-size: 16px;
  margin-right: 4px;
}

.back-text {
  font-size: 14px;
}

/* 关闭按钮 */
.nav-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: #999999;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-close-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  color: #666666;
}

.nav-close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 标题与说明文字区域 */
.phone-verify-header {
  text-align: center;
  padding: 0;
  margin-bottom: 32px;
  flex-shrink: 0;
}

.verify-title {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 12px;
  line-height: 1.2;
}

.verify-description {
  font-size: 14px;
  color: #000000;
  margin: 0;
  line-height: 1.4;
}

.phone-number {
  color: #1d1d1d;
  font-weight: 600;
}

/* 验证码输入区域 */
.phone-verify-form {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.verify-input-group {
  margin-bottom: 24px;
}

.verify-code-input {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

/* 验证码输入框 */
.verify-input {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #333333;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.verify-input:focus {
  outline: none;
  border-color: #0074e2;
  box-shadow: 0 0 0 2px rgba(0, 116, 226, 10%);
}

.verify-input.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 10%);
}

.verify-input::placeholder {
  color: #9ca3af;
}

/* 获取验证码按钮 */
.get-code-btn {
  background: none;
  border: none;
  color: #0074e2;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  white-space: nowrap;
  transition: all 0.2s ease;
  font-weight: 500;
}

.get-code-btn:hover:not(:disabled) {
  background-color: rgba(0, 116, 226, 10%);
}

.get-code-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
  background: none;
}

/* 错误提示文字 */
.error-text {
  display: block;
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 4px;
}

/* 确定按钮区域 */
.phone-verify-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 40px;
}

/* 确定按钮 */
.confirm-btn {
  width: 270px;
  height: 36px;
  background: #0084ff;
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn:hover:not(:disabled) {
  background: #0066cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 132, 255, 30%);
}

.confirm-btn:active:not(:disabled) {
  transform: translateY(0);
}

.confirm-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .phone-verify-card {
    max-width: 100%;
    margin: 16px;
    border-radius: 8px;
  }

  .phone-verify-nav {
    padding: 12px 16px;
  }

  .phone-verify-header {
    padding: 16px 24px 20px;
  }

  .verify-title {
    font-size: 18px;
  }

  .verify-description {
    font-size: 13px;
  }

  .phone-verify-form {
    padding: 0 24px 24px;
  }

  .verify-code-input {
    flex-direction: column;
    gap: 8px;
  }

  .get-code-btn {
    align-self: flex-end;
    padding: 6px 12px;
  }

  .confirm-btn {
    width: 100%;
    max-width: 270px;
  }
}

/* 动画效果 */
@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
