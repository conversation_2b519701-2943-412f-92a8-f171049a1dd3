/**
 * 登录模块类型定义
 */

// 登录表单数据接口
export interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
}

// 登录响应接口
export interface LoginResponse {
  success: boolean;
  message?: string;
  token?: string;
  needCaptcha?: boolean;
  needPhoneVerify?: boolean;
  userID?: string;
  expireTimeSeconds?: number;
  userInfo?: {
    id: string;
    username: string;
    email?: string;
    avatar?: string;
  };
}

// 登录表单组件属性
export interface LoginFormProps {
  onSubmit: (data: LoginFormData) => void;
  error?: string;
  isLoading?: boolean;
  showNetworkError?: boolean;
}

// 登录应用组件属性
export interface LoginAppProps {
  loginManager: LoginManager;
  onLoginSuccess: () => void;
}

// 登录步骤类型
export type LoginStep = 'form' | 'captcha' | 'phone' | 'loading';

// 网络状态类型
export type NetworkStatus = 'online' | 'offline';

// 验证错误接口
export interface ValidationErrors {
  username?: string;
  password?: string;
  general?: string;
}

// 登录管理器接口
export interface LoginManager {
  performLogin(username: string, password: string): Promise<LoginResponse>;
  performLoginWithCaptcha(loginData: LoginFormData, captchaData: any): Promise<LoginResponse>;
  performLoginWithPhone(loginData: LoginFormData, phoneData: any): Promise<LoginResponse>;
  saveUserSession(token: string, username: string): void;
  checkLoginStatus(): boolean;
  logout(): void;
}
