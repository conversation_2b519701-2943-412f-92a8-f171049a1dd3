/**
 * 登录模块入口文件
 * 统一管理所有登录相关组件和逻辑
 */

import React from 'react';
import ReactDOM from 'react-dom';
import { LoginApp } from './components/LoginApp';
import { LoginManager } from './services/LoginManager';
import './styles/login.css';
import './styles/design-login.css';

// 登录模块类
export class LoginModule {
  private readonly loginManager: LoginManager;

  private container: HTMLElement | null = null;

  constructor() {
    this.loginManager = new LoginManager();
    this.init();
  }

  private init() {
    // 监听登录页面就绪事件
    window.addEventListener('loginPageReady', () => {
      this.renderLoginApp();
    });

    // 暴露登录管理器到全局
    (window as any).LoginManager = this.loginManager;
  }

  private renderLoginApp() {
    // 清空现有内容
    document.body.innerHTML = '<div id="login-root"></div>';

    // 获取容器并渲染React组件
    this.container = document.getElementById('login-root');
    if (this.container) {
      ReactDOM.render(
        <LoginApp
          loginManager={this.loginManager}
          onLoginSuccess={this.handleLoginSuccess}
        />,
        this.container
      );
    }
  }

  private readonly handleLoginSuccess = () => {
    // 切换到业务窗口尺寸
    this.switchToBusinessSize();

    // 登录成功后的处理逻辑
    if ((window as any).handleLoginSuccess) {
      (window as any).handleLoginSuccess();
    }
  };

  // 切换到业务窗口尺寸
  private switchToBusinessSize() {
    if ((window as any).electronAPI?.ipcSend) {
      (window as any).electronAPI.ipcSend('switch-to-business-size');
    }
  }

  // 切换到登录窗口尺寸
  private switchToLoginSize() {
    if ((window as any).electronAPI?.ipcSend) {
      (window as any).electronAPI.ipcSend('switch-to-login-size');
    }
  }

  // 公共方法：检查登录状态
  public checkLoginStatus(): boolean {
    return this.loginManager.checkLoginStatus();
  }

  // 公共方法：登出
  public logout() {
    this.loginManager.logout();
  }

  // 销毁组件
  public destroy() {
    if (this.container) {
      ReactDOM.unmountComponentAtNode(this.container);
      this.container = null;
    }
  }
}

// 创建登录模块实例
const loginModule = new LoginModule();

// 暴露到全局
(window as any).LoginModule = loginModule;
