# 登录组件

根据设计稿实现的现代化登录界面组件。

## 组件结构

```
login/
├── components/
│   ├── LoginApp.tsx           # 原始登录应用主组件
│   ├── LoginForm.tsx          # 原始登录表单组件
│   ├── DesignLoginForm.tsx    # 根据设计稿实现的登录表单
│   ├── DesignLoginDemo.tsx    # 设计登录表单演示组件
│   ├── LoadingSpinner.tsx     # 加载动画组件
│   ├── CaptchaVerify.tsx      # 验证码组件
│   └── PhoneVerify.tsx        # 手机验证组件
├── services/
│   └── LoginManager.ts        # 登录管理服务
├── styles/
│   ├── login.css              # 原始登录样式
│   └── design-login.css       # 设计稿登录样式
├── types/
│   └── login.d.ts             # 类型定义
├── index.tsx                  # 登录模块入口
├── design-demo.tsx            # 设计演示入口
└── README.md                  # 说明文档
```

## 主要特性

### DesignLoginForm 组件

根据设计稿严格实现的登录表单，包含以下特性：

- ✅ **像素级还原**：严格按照设计稿的视觉规范
- ✅ **网络状态检测**：自动检测网络状态并显示异常提示
- ✅ **表单验证**：用户名/邮箱和密码的完整验证
- ✅ **密码可见性切换**：点击眼睛图标切换密码显示
- ✅ **自动登录功能**：记住用户选择和用户名
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **无障碍支持**：完整的键盘导航和屏幕阅读器支持
- ✅ **TypeScript 支持**：完整的类型定义和类型安全

## 使用方法

### 基础用法

```tsx
import React from 'react';
import { DesignLoginForm, LoginFormData } from './components/DesignLoginForm';
import './styles/design-login.css';

const App: React.FC = () => {
  const handleLogin = async (data: LoginFormData) => {
    console.log('登录数据:', data);
    // 处理登录逻辑
  };

  return (
    <div className="design-login-app">
      <DesignLoginForm
        onSubmit={handleLogin}
        error="网络异常，请检查网络设置"
        isLoading={false}
        showNetworkError={true}
      />
    </div>
  );
};
```

### 完整演示

```tsx
import React from 'react';
import { DesignLoginDemo } from './components/DesignLoginDemo';

const App: React.FC = () => {
  return <DesignLoginDemo />;
};
```

## 组件属性

### DesignLoginForm

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `onSubmit` | `(data: LoginFormData) => void` | - | 表单提交回调 |
| `error` | `string` | - | 错误信息 |
| `isLoading` | `boolean` | `false` | 加载状态 |
| `showNetworkError` | `boolean` | `false` | 显示网络错误 |

### LoginFormData

```typescript
interface LoginFormData {
  username: string;      // 用户名或邮箱
  password: string;      // 密码
  rememberMe: boolean;   // 自动登录
}
```

## 样式定制

### CSS 变量

可以通过 CSS 变量定制主题色彩：

```css
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --error-color: #ef4444;
  --border-color: #e5e7eb;
  --text-color: #111827;
  --placeholder-color: #9ca3af;
}
```

### 自定义样式

```css
/* 自定义登录按钮 */
.design-login-button {
  background: linear-gradient(135deg, #your-color 0%, #your-color-dark 100%);
}

/* 自定义输入框 */
.design-input {
  border-radius: 12px;
  border-width: 2px;
}
```

## 表单验证

组件包含以下验证规则：

### 用户名/邮箱验证
- 不能为空
- 长度至少2个字符
- 支持邮箱格式验证
- 支持用户名格式验证（字母、数字、下划线、连字符）

### 密码验证
- 不能为空
- 长度至少6个字符
- 长度不超过50个字符

## 网络状态检测

组件自动检测网络状态：

- 监听 `online` 和 `offline` 事件
- 自动显示网络异常提示
- 支持手动控制网络错误显示

## 本地存储

组件使用 localStorage 存储：

- `rememberMe`: 自动登录选择状态
- `savedUsername`: 保存的用户名

## 键盘支持

- `Enter`: 提交表单
- `Tab`: 导航到下一个元素
- `Space`: 切换复选框状态

## 浏览器兼容性

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 开发调试

### 运行演示

```bash
# 启动开发服务器
npm run start

# 在浏览器中打开演示页面
# 访问 design-demo.tsx 入口文件
```

### 测试网络错误

演示组件提供了网络错误切换按钮，可以测试不同的错误状态。

## 注意事项

1. **图标字体**: 组件使用 emoji 作为图标，确保字体支持
2. **样式隔离**: 使用独立的 CSS 文件避免样式冲突
3. **类型安全**: 确保传入正确的 TypeScript 类型
4. **响应式**: 在不同设备上测试界面效果

## 更新日志

### v1.0.0
- 根据设计稿实现基础登录界面
- 添加网络状态检测
- 实现表单验证和密码可见性切换
- 支持自动登录功能
- 完整的 TypeScript 类型定义
