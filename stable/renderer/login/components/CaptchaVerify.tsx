/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/button-has-type */
/**
 * 极验验证组件
 * 集成极验SDK实现验证功能
 */

import React, { useEffect, useRef, useState } from 'react';
import GeeTest from '../utils/geetest';

interface CaptchaVerifyProps {
  onSuccess: (captchaData: any) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

export const CaptchaVerify: React.FC<CaptchaVerifyProps> = ({
  onSuccess,
  onError,
  onCancel,
}) => {
  const captchaRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [geeTest] = useState(() => new GeeTest());

  useEffect(() => {
    // 初始化极验验证
    initGeetestCaptcha();

    return () => {
      // 清理极验实例
      cleanupGeetestCaptcha();
    };
  }, []);

  const initGeetestCaptcha = async () => {
    try {
      setIsLoading(true);

      // 使用GeeTest类进行极验验证
      const result = await geeTest.initGt();

      setIsLoading(false);

      if (result.retCode === 0) {
        // 验证成功
        onSuccess({
          challenge: result.value,
          success: true,
        });
      } else if (result.retCode === 100) {
        // 用户手动关闭
        onCancel();
      } else {
        // 验证失败
        onError(result.message || '验证失败，请重试');
      }
    } catch (error) {
      setIsLoading(false);
      console.error('极验验证初始化失败:', error);
      onError('验证初始化失败，请重试');
    }
  };

  const cleanupGeetestCaptcha = () => {
    // 清理极验实例
    if (window.geetestState) {
      window.geetestState = 'ready';
    }
    console.log('清理极验验证组件');
  };

  return (
    <div className="captcha-verify">
      <div className="captcha-header">
        <h3>安全验证</h3>
        <p>为了您的账户安全，请完成以下验证</p>
      </div>

      <div ref={captchaRef} className="captcha-widget" id="captcha-container">
        {isLoading ? (
          <div className="captcha-loading">
            <div className="loading-spinner"></div>
            <p>正在初始化验证组件...</p>
          </div>
        ) : (
          <div className="captcha-placeholder">
            <p>验证组件已加载</p>
            <p className="captcha-note">
              请按照提示完成安全验证
            </p>
          </div>
        )}
      </div>

      <div className="captcha-actions">
        <button className="back-btn" onClick={onCancel} disabled={isLoading}>
          返回登录
        </button>
      </div>
    </div>
  );
};
