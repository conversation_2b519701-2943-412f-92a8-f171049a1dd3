/**
 * 基础登录表单组件 - 根据设计稿重新实现
 */

import React, { useState, useCallback, useEffect } from 'react';
import { isValidEmployeeCode } from '../utils/utils';

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => void;
  error?: string;
  isLoading?: boolean;
}

export interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  error,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<LoginFormData>({
    username: '',
    password: '',
    rememberMe: false,
  });

  const [validationErrors, setValidationErrors] = useState<{
    username?: string;
    password?: string;
  }>({});

  const [showPassword, setShowPassword] = useState(false);

  // 从本地存储恢复记住的用户名
  useEffect(() => {
    const rememberMe = localStorage.getItem('rememberMe') === 'true';
    const savedUsername = localStorage.getItem('savedUsername') || '';

    if (rememberMe && savedUsername) {
      setFormData((prev) => ({
        ...prev,
        username: savedUsername,
        rememberMe: true,
      }));
    }
  }, []);

  const validateForm = useCallback((): boolean => {
    const errors: typeof validationErrors = {};

    // 工号验证
    if (!formData.username.trim()) {
      errors.username = '请输入工号';
    } else if (!isValidEmployeeCode(formData.username)) {
      errors.username = '请输入有效的工号（4-8位数字）';
    }

    // 密码验证
    if (!formData.password.trim()) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少6个字符';
    } else if (formData.password.length > 50) {
      errors.password = '密码不能超过50个字符';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  const handleInputChange = useCallback(
    (field: keyof LoginFormData) => {
      return (e: React.ChangeEvent<HTMLInputElement>) => {
        const value =
          field === 'rememberMe' ? e.target.checked : e.target.value;

        setFormData((prev) => ({
          ...prev,
          [field]: value,
        }));

        // 清除对应字段的验证错误
        if (validationErrors[field as keyof typeof validationErrors]) {
          setValidationErrors((prev) => ({
            ...prev,
            [field]: undefined,
          }));
        }
      };
    },
    [validationErrors]
  );

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      // 保存用户名（如果选择了自动登录）
      if (formData.rememberMe) {
        localStorage.setItem('savedUsername', formData.username);
      } else {
        localStorage.removeItem('savedUsername');
      }

      onSubmit(formData);
    },
    [formData, validateForm, onSubmit]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleSubmit(e as any);
      }
    },
    [handleSubmit]
  );

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword((prev) => !prev);
  }, []);

  return (
    <div className="design-login-form">
      {/* 登录标题 */}
      <h1 className="design-login-title">登录Linkflow</h1>
      
      {/* 网络异常提示 */}
      {error && (
        <div className="design-error-alert">
          <div className="error-icon">⚠</div>
          <span className="error-message">网络异常，请检查网络设置</span>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* 用户名输入框 */}
        <div className="design-input-group">
          <input
            type="text"
            value={formData.username}
            onChange={handleInputChange('username')}
            onKeyDown={handleKeyDown}
            placeholder="请输入工号"
            disabled={isLoading}
            className={`design-input ${
              validationErrors.username ? 'error' : ''
            }`}
          />
          {validationErrors.username && (
            <div className="design-field-error">
              {validationErrors.username}
            </div>
          )}
        </div>

        {/* 密码输入框 */}
        <div className="design-input-group">
          <div className="design-password-wrapper">
            <input
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              onKeyDown={handleKeyDown}
              placeholder="请输入OA密码"
              disabled={isLoading}
              className={`design-input design-password-input ${
                validationErrors.password ? 'error' : ''
              }`}
            />
            <button
              type="button"
              className="design-password-toggle"
              onClick={togglePasswordVisibility}
              disabled={isLoading}
              title={showPassword ? '隐藏密码' : '显示密码'}
            >
              <span className="eye-icon">
                {showPassword ? (
                  // 隐藏状态 - 闭眼图标
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M2 12C2 12 5 5 12 5C19 5 22 12 22 12C22 12 19 19 12 19C5 19 2 12 2 12Z" stroke="currentColor" strokeWidth="2" fill="none"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" fill="none"/>
                  </svg>
                ) : (
                  // 显示状态 - 闭眼图标加斜线
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M2 12C2 12 5 5 12 5C19 5 22 12 22 12C22 12 19 19 12 19C5 19 2 12 2 12Z" stroke="currentColor" strokeWidth="2" fill="none"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" fill="none"/>
                    <line x1="2" y1="2" x2="22" y2="22" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                )}
              </span>
            </button>
          </div>
          {validationErrors.password && (
            <div className="design-field-error">
              {validationErrors.password}
            </div>
          )}
        </div>

        {/* 登录按钮 */}
        <button
          type="submit"
          className="design-login-button"
          disabled={isLoading}
        >
          {isLoading ? '登录中...' : '登录'}
        </button>

        {/* 自动登录复选框 */}
        <div className="design-auto-login">
          <label className="design-checkbox-label">
            <input
              type="checkbox"
              checked={formData.rememberMe}
              onChange={handleInputChange('rememberMe')}
              disabled={isLoading}
              className="design-checkbox-input"
            />
            <span className="design-checkbox-custom"></span>
            <span className="design-checkbox-text">自动登录</span>
          </label>
        </div>
      </form>
    </div>
  );
};
