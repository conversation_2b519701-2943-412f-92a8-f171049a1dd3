/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/button-has-type */
/* eslint-disable no-lonely-if */
/**
 * 登录应用主组件
 * 管理登录流程和不同登录组件的切换
 */

import React, { useState, useCallback, useEffect } from 'react';
import { LoginForm } from './LoginForm';
import { LoadingSpinner } from './LoadingSpinner';
import { CaptchaVerify } from './CaptchaVerify';
import { PhoneVerify } from './PhoneVerify';
import { LoginManager } from '../services/LoginManager';
import GeeTest from '../utils/geetest';
import { apiService } from '../services/apiService';

interface LoginAppProps {
  loginManager: LoginManager;
  onLoginSuccess: () => void;
}

export type LoginStep = 'form' | 'captcha' | 'phone' | 'loading';

export const LoginApp: React.FC<LoginAppProps> = ({
  loginManager,
  onLoginSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState<LoginStep>('form');
  const [loginData, setLoginData] = useState<any>(null);
  const [challengeValue, setChallengeValue] = useState();
  const [error, setError] = useState<string>('');
  const [phoneVerifyData, setPhoneVerifyData] = useState<{ mobile?: string }>(
    {}
  );

  // 检查自动登录
  useEffect(() => {
    if (loginManager.canAutoLogin()) {
      console.log('检测到自动登录，直接跳转到主应用');
      onLoginSuccess();
    }
  }, [loginManager, onLoginSuccess]);

  const handleLogin = useCallback(
    (_loginData: any, _challengeValue: any) => {
      // login接口和用户login信息以及challenge号发起登录
      apiService
        .login({
          employeeCode: _loginData.username,
          password: _loginData.password,
          challenge: _challengeValue,
        })
        .then((res) => {
          console.log('登录响应:', res);

          // 处理不同的响应情况
          switch (res.code) {
            case 0:
              // 登录成功
              if (res.data) {
                const loginResponse = res.data as any;
                const { token, expireTimeSeconds, userID } = loginResponse;
                console.log('登录成功:', { token, expireTimeSeconds, userID });

                // 保存用户会话信息，如果用户勾选了自动登录则保存token
                loginManager.saveUserSession(
                  token,
                  _loginData.username,
                  expireTimeSeconds,
                  _loginData.rememberMe
                );

                // 跳转到主应用
                onLoginSuccess();
              }
              break;

            case 1000:
              // 需要两步验证
              if (res.data) {
                const twoStepData = res.data as any;
                if (twoStepData.mobile) {
                  console.log('需要两步验证，手机号:', twoStepData.mobile);
                  setPhoneVerifyData({ mobile: twoStepData.mobile });
                  setCurrentStep('phone');
                } else {
                  setError('需要两步验证，但未获取到手机号信息');
                  setCurrentStep('form');
                }
              } else {
                setError('需要两步验证，但未获取到手机号信息');
                setCurrentStep('form');
              }
              break;

            case 1010:
              // 账号密码认证失败
              setError('账号或密码错误，请重新输入');
              setCurrentStep('form');
              break;

            case 1015:
              // 账号未绑定手机号
              setError('账号未绑定手机号，请联系管理员绑定手机号后重试');
              setCurrentStep('form');
              break;

            default:
              // 其他错误
              setError(res.msg || '登录失败，请稍后重试');
              setCurrentStep('form');
              break;
          }
        })
        .catch((err) => {
          console.error('登录请求失败:', err);
          setError('网络连接失败，请检查网络设置后重试');
          setCurrentStep('form');
        });
    },
    [loginManager, onLoginSuccess, setPhoneVerifyData]
  );

  const handleFormSubmit = useCallback(
    async (formData: any) => {
      setError('');
      setCurrentStep('loading');
      setLoginData(formData);
      console.log('formData', formData);

      new GeeTest().initGt().then((res) => {
        //
        console.log(' new GeeTest res', res);

        if (res?.retCode === 0) {
          // 拿到流水号
          setChallengeValue(res.value);
          // 发起登录请求
          handleLogin(formData, res.value);
        } else if (res.retCode === 99) {
          // 用户未通过人机行为验证
          // gt插件验证失败，请重试
          setError('人机验证失败，请重试');
          setCurrentStep('form');
        } else if (res.retCode === 100) {
          // 用户手动关闭行为验证
          setError('已取消验证');
          setCurrentStep('form');
        } else {
          // 这里是调用后台验证流水号失败的接口返回
          setError('验证失败，请重试');
          setCurrentStep('form');
        }
      });
    },
    [handleLogin]
  );

  const handleCaptchaSuccess = useCallback(
    async (captchaData: any) => {
      // 处理极验验证成功
      setCurrentStep('loading');

      try {
        const result = await loginManager.performLoginWithCaptcha(
          loginData,
          captchaData
        );

        if (result.success) {
          loginManager.saveUserSession(result.token, loginData.username);
          onLoginSuccess();
        } else if (result.needPhoneVerify) {
          setCurrentStep('phone');
        } else {
          setError(result.message || '验证失败');
          setCurrentStep('form');
        }
      } catch (captchaError) {
        setError('验证失败，请重试');
        setCurrentStep('form');
      }
    },
    [loginManager, loginData, onLoginSuccess]
  );

  const handlePhoneVerifySuccess = useCallback(
    async (phoneData: any) => {
      // 处理手机验证成功 - 这里phoneData包含了验证码登录的响应数据
      console.log('手机验证成功:', phoneData);

      // 验证码登录成功后，PhoneVerify组件已经调用了loginWithCode接口
      // 这里直接处理登录成功的逻辑
      if (loginData && phoneData.token) {
        loginManager.saveUserSession(
          phoneData.token,
          loginData.username,
          phoneData.expireTimeSeconds,
          loginData.rememberMe
        );
        onLoginSuccess();
      } else {
        setError('登录数据缺失或token无效，请重新登录');
        setCurrentStep('form');
      }
    },
    [loginManager, loginData, onLoginSuccess]
  );

  const handleBackToForm = useCallback(() => {
    setCurrentStep('form');
    setError('');
  }, []);

  const handleCloseWindow = useCallback(() => {
    // 关闭窗口
    if ((window as any).electronAPI?.ipcSend) {
      (window as any).electronAPI.ipcSend('close-window');
    } else {
      window.close();
    }
  }, []);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'form':
        return (
          <LoginForm
            onSubmit={handleFormSubmit}
            error={error}
            isLoading={false}
          />
        );

      case 'captcha':
        return (
          <CaptchaVerify
            onSuccess={handleCaptchaSuccess}
            onError={(errorMsg) => {
              setError(errorMsg);
              setCurrentStep('form');
            }}
            onCancel={handleBackToForm}
          />
        );

      case 'phone':
        return (
          <PhoneVerify
            onSuccess={handlePhoneVerifySuccess}
            onError={(errorMsg) => {
              setError(errorMsg);
              setCurrentStep('form');
            }}
            onCancel={handleBackToForm}
            mobile={phoneVerifyData.mobile}
            loginData={{
              ...loginData,
              challenge: challengeValue,
            }}
          />
        );

      case 'loading':
        return <LoadingSpinner message="正在登录..." />;

      default:
        return null;
    }
  };

  return (
    <div className="design-login-app">
      <div className="design-login-container">
        {/* 右上角关闭按钮 */}
        <button
          className="close-button"
          type="button"
          onClick={handleCloseWindow}
          title="关闭"
        >
          ✕
        </button>

        <div className="login-content">{renderCurrentStep()}</div>
      </div>
    </div>
  );
};
