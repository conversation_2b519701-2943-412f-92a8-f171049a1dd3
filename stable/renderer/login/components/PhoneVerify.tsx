/**
 * 手机号验证组件
 * 实现两步验证的手机号验证逻辑
 */

import React, { useState, useCallback, useEffect } from 'react';
import { apiService } from '../services/apiService';

interface PhoneVerifyProps {
  onSuccess: (phoneData: PhoneVerifyData) => void;
  onError: (error: string) => void;
  onCancel: () => void;
  mobile?: string; // 从登录接口返回的手机号
  loginData?: any; // 登录数据，用于发送验证码和验证码登录
}

export interface PhoneVerifyData {
  phoneNumber: string;
  verifyCode: string;
  timestamp: number;
  token?: string;
  expireTimeSeconds?: number;
  userID?: string;
}

export const PhoneVerify: React.FC<PhoneVerifyProps> = ({
  onSuccess,
  onError,
  onCancel,
  mobile,
  loginData,
}) => {
  const [verifyCode, setVerifyCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    verifyCode?: string;
  }>({});

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleSendCode = useCallback(async () => {
    if (!loginData) {
      onError('登录数据缺失，请重新登录');
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // 调用发送验证码API
      await apiService.resendCode({
        platformID: 1,
        deviceID: loginData.deviceID || 'default-device-id',
        employeeCode: loginData.username,
        password: loginData.password,
        challenge: loginData.challenge,
      });

      setCountdown(60); // 60秒倒计时
      console.log('验证码已发送到:', mobile);
    } catch (error) {
      onError('发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  }, [loginData, mobile, onError]);

  const handleVerify = useCallback(async () => {
    const newErrors: typeof errors = {};

    if (!verifyCode.trim()) {
      newErrors.verifyCode = '请输入验证码';
    } else if (verifyCode.length !== 6) {
      newErrors.verifyCode = '验证码应为6位数字';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    if (!loginData) {
      onError('登录数据缺失，请重新登录');
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // 调用验证码登录API
      const response = await apiService.loginWithCode({
        platformID: 1,
        deviceID: loginData.deviceID || 'default-device-id',
        employeeCode: loginData.username,
        password: loginData.password,
        code: verifyCode,
      });

      if (response.code === 0 && response.data) {
        const { token, expireTimeSeconds, userID } = response.data;
        onSuccess({
          phoneNumber: mobile || '',
          verifyCode,
          timestamp: Date.now(),
          token,
          expireTimeSeconds,
          userID,
        });
      } else {
        onError(response.msg || '验证码错误，请重新输入');
      }
    } catch (error) {
      onError('验证失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  }, [verifyCode, loginData, mobile, onSuccess, onError]);

  return (
    <div className="phone-verify">
      <div className="phone-verify-header">
        <h3>通过手机号验证身份</h3>
        <p>为了你的账号安全，请先验证手机号，验证码将发送至 {mobile}</p>
      </div>

      <div className="phone-verify-form">

        <div className="form-group">
          <label htmlFor="verifyCode">验证码</label>
          <div className="verify-code-input">
            <input
              id="verifyCode"
              type="text"
              value={verifyCode}
              onChange={(e) => setVerifyCode(e.target.value)}
              placeholder="请输入6位验证码"
              disabled={isLoading}
              maxLength={6}
              className={errors.verifyCode ? 'error' : ''}
            />
            <button
              type="button"
              className="send-code-btn"
              onClick={handleSendCode}
              disabled={isLoading || countdown > 0}
            >
              {countdown > 0 ? `${countdown}s` : '获取验证码'}
            </button>
          </div>
          {errors.verifyCode && (
            <span className="error-text">{errors.verifyCode}</span>
          )}
        </div>

        <div className="phone-verify-actions">
          <button
            type="button"
            className="verify-btn"
            onClick={handleVerify}
            disabled={isLoading || !verifyCode}
          >
            {isLoading ? '验证中...' : '确定'}
          </button>

          <button
            type="button"
            className="back-btn"
            onClick={onCancel}
            disabled={isLoading}
          >
            返回登录
          </button>
        </div>
      </div>
    </div>
  );
};
