{"name": "Linkflow", "productName": "Linkflow", "version": "0.15.0", "description": "Linkflow", "main": ".webpack/main", "scripts": {"start": "npm run start:sit", "start:dev": "cross-env SERVER=development electron-forge start", "start:sit": "cross-env SERVER=sit electron-forge start", "start:uat": "cross-env SERVER=uat electron-forge start", "package": "npm run package:sit", "package:sit": "cross-env NODE_ENV=development SERVER=sit electron-forge package", "package:uat": "cross-env NODE_ENV=development SERVER=uat electron-forge package", "package:prod": "cross-env NODE_ENV=production SERVER=prod electron-forge package", "make": "npm run make:sit:win", "make:sit:win": "cross-env NODE_ENV=production SERVER=sit electron-forge make", "make:sit:mac": "cross-env NODE_ENV=production SERVER=sit electron-forge make --arch=universal", "make:uat:win": "cross-env NODE_ENV=production SERVER=uat electron-forge make", "make:uat:mac": "cross-env NODE_ENV=production SERVER=uat electron-forge make --arch=universal", "make:prod:win": "cross-env NODE_ENV=production SERVER=prod electron-forge make", "make:prod:mac": "cross-env NODE_ENV=production SERVER=prod electron-forge make --arch=universal", "make:lite:win": "cross-env NODE_ENV=production SERVER=lite electron-forge make", "make:lite:mac": "cross-env NODE_ENV=production SERVER=lite electron-forge make --arch=universal", "publish": "electron-forge publish", "prepare": "husky install && node ./.husky/prepare.js"}, "keywords": ["electron"], "author": {"name": "HTSC", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/eslint-parser": "^7.18.9", "@babel/eslint-plugin": "^7.13.10", "@electron-forge/cli": "^6.1.1", "@electron-forge/maker-deb": "^6.1.1", "@electron-forge/maker-dmg": "^6.0.5", "@electron-forge/maker-rpm": "^6.1.1", "@electron-forge/maker-squirrel": "^6.1.1", "@electron-forge/maker-zip": "^6.1.1", "@electron-forge/maker-wix": "^6.0.0-beta.74", "@electron-forge/plugin-webpack": "^6.1.1", "@ht/eslint-config-htsc": "2.0.20", "@modern-js-app/eslint-config": "1.2.4", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "@vercel/webpack-asset-relocator-loader": "1.7.3", "concurrently": "^8.0.1", "cross-env": "^7.0.3", "css-loader": "^6.0.0", "electron": "^36.3.1", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-webpack": "^0.13.1", "eslint-plugin-eslint-comments": "^3.1.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-markdown": "^2.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-vue": "8.7.1", "fork-ts-checker-webpack-plugin": "^7.2.13", "husky": "7.0.4", "lint-staged": "^10.0.7", "node-loader": "^2.0.0", "postcss": "^8.4.16", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.7.1", "style-loader": "^3.0.0", "stylelint": "^14.9.1", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^26.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-prettier": "^2.0.0", "ts-loader": "^9.2.2", "ts-node": "^10.0.0", "typescript": "^4.0.0", "wait-on": "^7.0.1", "yorkie": "^2.0.0"}, "dependencies": {"@ht/openim-electron-client-sdk": "0.15.0-beta.1", "@ht/openim-wasm-client-sdk": "0.12.0-beta.dev.6", "axios": "1.4.0", "crypto-js": "^4.1.1", "electron-log": "^4.4.8", "electron-squirrel-startup": "^1.0.0", "electron-updater": "^5.3.0", "fs-extra": "^11.1.1", "ip": "^2.0.1", "js-md5": "^0.7.3", "lodash": "^4.17.21", "lowdb": "^6.0.1", "moment": "^2.29.4", "react": "^17.0.0", "react-dom": "^17.0.0"}}